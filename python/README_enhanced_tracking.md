# 增强雷达跟踪系统

本项目实现了三个主要的增强功能来改进雷达目标跟踪效果：

## 🚀 新功能

### 1. 120帧分组处理
- **功能**: 将雷达每120帧数据（一圈扫描）作为一组输入到跟踪器
- **优势**: 
  - 减少数据稀疏性问题
  - 提高跟踪稳定性
  - 更好地利用雷达扫描周期特性

### 2. 动态3D可视化
- **功能**: 实时显示跟踪过程的3D动画
- **特性**:
  - 红色虚线显示真实目标轨迹
  - 彩色实线显示跟踪结果
  - 红色三角形标记真实目标当前位置
  - 彩色X标记跟踪目标位置
  - 支持轨迹历史显示

### 3. 轨迹插值和平滑
- **功能**: 为120帧输入但单次输出的跟踪结果生成平滑轨迹
- **方法**:
  - 线性插值 (linear)
  - 三次样条插值 (cubic)  
  - 卡尔曼滤波插值 (kalman)
- **效果**: 将稀疏的跟踪输出扩展为平滑连续的轨迹

## 📁 文件结构

```
python/
├── trackerv2.py                    # 主要跟踪器代码（增强版）
├── test_enhanced_tracking.py       # 测试脚本
├── demo_enhanced_features.py       # 功能演示脚本
├── README_enhanced_tracking.md     # 本说明文档
└── radar_simulation_data_1.csv     # 测试数据
```

## 🔧 使用方法

### 基本测试

```bash
# 正常逐帧跟踪
python test_enhanced_tracking.py normal

# 120帧分组跟踪
python test_enhanced_tracking.py grouped

# 分组跟踪 + 插值平滑
python test_enhanced_tracking.py interpolated

# 动态3D可视化
python test_enhanced_tracking.py animated

# 比较不同模式
python test_enhanced_tracking.py compare
```

### 功能演示

```bash
# 运行完整功能演示
python demo_enhanced_features.py
```

### 在代码中使用

```python
from trackerv2 import (
    load_radar_data_from_csv,
    main_test,
    smooth_tracking_results,
    visualize_animated_3d
)

# 1. 120帧分组处理
frames, frame_info = load_radar_data_from_csv(
    'radar_data.csv',
    group_frames=True,
    frames_per_group=120
)

# 2. 运行跟踪测试
frames, tracked_history, metrics = main_test(use_grouped_frames=True)

# 3. 轨迹插值平滑
smoothed_results = smooth_tracking_results(
    tracked_history, frame_info,
    interpolation_points=5,
    method='cubic'
)

# 4. 动态3D可视化
visualize_animated_3d(frames, tracked_history, max_trace_length=20)
```

## 📊 功能对比

| 功能 | 原版 | 增强版 |
|------|------|--------|
| 数据处理 | 逐帧处理 | 支持120帧分组 |
| 可视化 | 静态图表 | 动态3D动画 |
| 轨迹输出 | 稀疏点 | 平滑插值轨迹 |
| 跟踪稳定性 | 一般 | 显著提升 |

## 🎯 应用场景

1. **雷达目标跟踪**: 适用于旋转扫描雷达的目标跟踪
2. **轨迹分析**: 需要平滑连续轨迹的应用
3. **实时监控**: 需要动态可视化的监控系统
4. **性能评估**: 比较不同跟踪算法的效果

## ⚙️ 参数调整

### 分组处理参数
- `frames_per_group`: 每组帧数（默认120）
- `group_frames`: 是否启用分组模式

### 插值参数
- `interpolation_points`: 插值点数（默认5）
- `method`: 插值方法 ('linear', 'cubic', 'kalman')

### 可视化参数
- `max_trace_length`: 最大轨迹长度（默认100）
- `save_animation`: 是否保存动画

## 🔍 性能指标

系统会自动计算以下性能指标：
- 精确度 (Precision)
- 召回率 (Recall)
- F1分数
- MOTA (多目标跟踪精度)
- MOTP (多目标跟踪准确度)
- 平均位置误差

## 📝 注意事项

1. 动态可视化需要交互式环境
2. 插值功能需要至少2个跟踪点
3. 120帧分组适用于6秒扫描周期的雷达
4. 大数据集建议使用分组模式以提高效率

## 🐛 故障排除

如果遇到问题：
1. 检查CSV数据格式是否正确
2. 确保安装了所需的Python包
3. 检查数据路径是否正确
4. 查看控制台输出的错误信息
